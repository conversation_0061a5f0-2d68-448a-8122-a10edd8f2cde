# DhanHQ Enhanced Live Data Fetcher - Requirements
# ================================================

# Core DhanHQ API Library
#dhanhq>=2.2.0

# Data Processing and Analysis
pandas>=2.0.0
numpy>=1.24.0

# Date and Time Handling (built-in modules, but listing for clarity)
# datetime - built-in
# time - built-in
# os - built-in

# Optional: Enhanced Data Analysis (if you want to extend functionality)
# matplotlib>=3.7.0
# seaborn>=0.12.0
# plotly>=5.15.0

# Optional: Advanced Data Processing (if you want to extend functionality)
# scipy>=1.10.0
# scikit-learn>=1.3.0

# Optional: Database Support (if you want to store data in databases)
# sqlalchemy>=2.0.0
# pymongo>=4.4.0
# psycopg2-binary>=2.9.0

# Optional: Web Interface (if you want to create a web dashboard)
# flask>=2.3.0
# streamlit>=1.25.0
# dash>=2.11.0

# Optional: Configuration Management
# python-dotenv>=1.0.0
# configparser - built-in

# Optional: Logging and Monitoring
# loguru>=0.7.0

# Optional: Testing (for development)
# pytest>=7.4.0
# pytest-cov>=4.1.0

# Optional: Code Quality (for development)
# black>=23.7.0
# flake8>=6.0.0
# mypy>=1.5.0

# Python Version Requirement
# python>=3.8
