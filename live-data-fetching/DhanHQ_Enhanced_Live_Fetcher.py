#!/usr/bin/env python3
"""
Enhanced DhanHQ Live Data Fetcher
- Continuous operation waiting for market hours
- 5 trading days of historical data management
- Live data collection during market hours
- Comprehensive timeframe support (1min, 5min, 15min, 25min, 60min, daily)
- Rolling window data management with 3-day daily data retention
"""

import os
import pandas as pd
import datetime
import time
from dhanhq import dhanhq

# ============================================================================
# CONFIGURATION
# ============================================================================

# API Configuration
CLIENT_ID = "1105577608"
ACCESS_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

# Data Management Configuration
HISTORICAL_DAYS = 5  # Keep last 5 trading days of intraday data
DAILY_DATA_DAYS = 3  # Keep last 3 days of daily data (rolling window)
LIVE_DATA_INTERVAL = 30  # Fetch live quotes every 30 seconds
INTRADAY_UPDATE_INTERVAL = 60  # Update intraday data every 60 seconds

# Timing Configuration for Accurate OHLC Data Collection
MINUTE_COMPLETION_BUFFER = 5  # Wait 5 seconds after minute completion for data accuracy
MINUTE_SYNC_TOLERANCE = 2  # Allow 2 seconds tolerance for minute boundary detection

# Market Timing
MARKET_OPEN = datetime.time(9, 15)
MARKET_CLOSE = datetime.time(15, 30)
PRE_MARKET_START = datetime.time(9, 0)

# API Configuration - Optimized for DhanHQ v2.2 (no rate limits on minute timeframes)
API_RATE_LIMIT_DELAY = 0.05
MAX_RETRIES = 2
RETRY_DELAY = 0.5

# Stock Configuration - 5 different stocks + NIFTY 50
STOCKS = [
    {
        'name': 'RELIANCE', 
        'security_id': '2885', 
        'exchange_segment': 'NSE_EQ', 
        'instrument_type': 'EQUITY'
    },
    {
        'name': 'TCS', 
        'security_id': '11536', 
        'exchange_segment': 'NSE_EQ', 
        'instrument_type': 'EQUITY'
    },
    {
        'name': 'INFY', 
        'security_id': '1594', 
        'exchange_segment': 'NSE_EQ', 
        'instrument_type': 'EQUITY'
    },
    {
        'name': 'HDFCBANK', 
        'security_id': '1333', 
        'exchange_segment': 'NSE_EQ', 
        'instrument_type': 'EQUITY'
    },
    {
        'name': 'ICICIBANK', 
        'security_id': '4963', 
        'exchange_segment': 'NSE_EQ', 
        'instrument_type': 'EQUITY'
    },
    {
        "name": "NIFTY 50",
        "security_id": "13",
        "exchange_segment": "IDX_I",
        "instrument_type": "INDEX"
    }
]

# Timeframe Configuration
TIMEFRAMES = {
    '1min': 1,
    '5min': 5,
    '15min': 15,
    '25min': 25,
    '60min': 60,
    'daily': 'daily'
}

# Initialize Dhan client
dhan = dhanhq(CLIENT_ID, ACCESS_TOKEN)

# Create directories
os.makedirs('stock_data', exist_ok=True)
os.makedirs('logs', exist_ok=True)

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def get_current_time():
    """Get current datetime"""
    return datetime.datetime.now()

def get_today():
    """Get today's date"""
    return datetime.datetime.now().date()

def wait_for_minute_completion():
    """
    Wait for the current minute to complete and add buffer time for accurate OHLC data.
    This ensures we collect data only after the minute has fully formed.
    """
    current_time = get_current_time()
    current_second = current_time.second

    # If we're already past the minute boundary, wait for next minute
    if current_second >= 60 - MINUTE_SYNC_TOLERANCE:
        # Wait until next minute starts
        next_minute = current_time.replace(second=0, microsecond=0) + datetime.timedelta(minutes=1)
        wait_seconds = (next_minute - current_time).total_seconds()
        if wait_seconds > 0:
            time.sleep(wait_seconds)
        # Add buffer time for data accuracy
        time.sleep(MINUTE_COMPLETION_BUFFER)
        return next_minute.replace(second=0, microsecond=0)
    else:
        # Wait for current minute to complete
        next_minute = current_time.replace(second=0, microsecond=0) + datetime.timedelta(minutes=1)
        wait_seconds = (next_minute - current_time).total_seconds()
        if wait_seconds > 0:
            time.sleep(wait_seconds)
        # Add buffer time for data accuracy
        time.sleep(MINUTE_COMPLETION_BUFFER)
        return next_minute.replace(second=0, microsecond=0)

def get_last_completed_minute():
    """
    Get the timestamp of the last completed minute.
    This is used to ensure we're fetching data for completed minutes only.
    """
    current_time = get_current_time()
    # Go back to the previous minute boundary
    last_minute = current_time.replace(second=0, microsecond=0)
    if current_time.second < MINUTE_COMPLETION_BUFFER:
        # If we're within the buffer period, go back one more minute
        last_minute = last_minute - datetime.timedelta(minutes=1)
    return last_minute

def is_minute_boundary_reached():
    """
    Check if we've reached a minute boundary and enough buffer time has passed
    for accurate OHLC data collection.
    """
    current_time = get_current_time()
    return current_time.second >= MINUTE_COMPLETION_BUFFER

def get_last_processed_minute_from_data():
    """
    Get the last processed minute from existing data files to avoid gaps.
    Returns the latest timestamp found across all 1-minute data files.
    """
    latest_minute = None

    for stock in STOCKS:
        filename = get_filename(stock, '1min')
        if os.path.exists(filename):
            try:
                df = pd.read_csv(filename)
                if not df.empty:
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    stock_latest = df['timestamp'].max()
                    if latest_minute is None or stock_latest > latest_minute:
                        latest_minute = stock_latest
            except Exception as e:
                log_event(stock['name'], '1min', 'ERROR', f'Error reading last minute: {str(e)}')

    return latest_minute

def get_next_minute_to_process(last_processed, current_time):
    """
    Determine the next minute that should be processed based on current time
    and last processed minute. Only returns a minute if enough time has passed
    for the candle to be complete.
    """
    # Calculate the current minute boundary
    current_minute_boundary = current_time.replace(second=0, microsecond=0)

    # If we're still within the buffer period of the current minute,
    # we should process the previous minute
    if current_time.second < MINUTE_COMPLETION_BUFFER:
        target_minute = current_minute_boundary - datetime.timedelta(minutes=1)
    else:
        target_minute = current_minute_boundary

    # If no last processed minute, start from target minute
    if last_processed is None:
        return target_minute

    # If target minute is after last processed, return it
    if target_minute > last_processed:
        return target_minute

    return None

def process_minute_data(target_minute):
    """
    Process all timeframes for a specific completed minute.
    This ensures accurate OHLC data for each timeframe.
    """
    for stock in STOCKS:
        for timeframe in TIMEFRAMES:
            if timeframe != 'daily':
                # For 1-minute data, always process
                if timeframe == '1min':
                    update_historical_data_for_minute(stock, timeframe, target_minute)
                else:
                    # For other timeframes, check if this minute completes the timeframe
                    if is_timeframe_complete(target_minute, timeframe):
                        # Calculate the timeframe boundary
                        tf_minute = get_timeframe_boundary(target_minute, timeframe)
                        update_historical_data_for_minute(stock, timeframe, tf_minute)

def is_timeframe_complete(minute, timeframe):
    """
    Check if the given minute completes a timeframe candle.
    For example, 09:35:00 completes a 5-minute candle (09:30-09:35).
    """
    if timeframe == '1min':
        return True

    interval = TIMEFRAMES[timeframe]
    minute_of_day = minute.hour * 60 + minute.minute
    market_start_minute = MARKET_OPEN.hour * 60 + MARKET_OPEN.minute

    # Calculate minutes since market open
    minutes_since_open = minute_of_day - market_start_minute

    # Check if this minute completes an interval
    return (minutes_since_open + 1) % interval == 0

def get_timeframe_boundary(minute, timeframe):
    """
    Get the boundary timestamp for a timeframe.
    For example, for 5-min timeframe at 09:35, return 09:35 (end of 09:30-09:35 candle).
    """
    if timeframe == '1min':
        return minute

    interval = TIMEFRAMES[timeframe]
    minute_of_day = minute.hour * 60 + minute.minute
    market_start_minute = MARKET_OPEN.hour * 60 + MARKET_OPEN.minute

    # Calculate the timeframe boundary
    minutes_since_open = minute_of_day - market_start_minute
    boundary_minutes = ((minutes_since_open // interval) + 1) * interval
    boundary_minute_of_day = market_start_minute + boundary_minutes

    boundary_hour = boundary_minute_of_day // 60
    boundary_min = boundary_minute_of_day % 60

    return minute.replace(hour=boundary_hour, minute=boundary_min, second=0, microsecond=0)

def log_event(stock, timeframe, event_type, message):
    """Log events to CSV file with timestamp"""
    timestamp = get_current_time().strftime('%Y-%m-%d %H:%M:%S')
    log_entry = f"{timestamp},{stock},{timeframe},{event_type},{message}\n"
    
    log_file = 'logs/activity_log.csv'
    if not os.path.exists(log_file):
        with open(log_file, 'w') as f:
            f.write("timestamp,stock,timeframe,event_type,message\n")
    
    with open(log_file, 'a') as f:
        f.write(log_entry)
    
    print(f"[{timestamp}] {stock} {timeframe}: {event_type} - {message}")

def is_trading_day(date):
    """Check if given date is a trading day (Monday to Friday)"""
    return date.weekday() < 5  # Monday=0, Sunday=6

def is_market_open():
    """Check if market is currently open"""
    current_time = get_current_time().time()
    return MARKET_OPEN <= current_time <= MARKET_CLOSE

def get_trading_days(days_back=5):
    """Get list of last N trading days"""
    trading_days = []
    current_date = get_today()
    
    while len(trading_days) < days_back:
        if is_trading_day(current_date):
            trading_days.append(current_date)
        current_date -= datetime.timedelta(days=1)
    
    return sorted(trading_days)

def get_filename(stock, timeframe):
    """Generate filename for stock data"""
    if timeframe == 'daily':
        return f"stock_data/{stock['name']}_daily.csv"
    else:
        return f"stock_data/{stock['name']}_{timeframe}.csv"

def wait_for_market_open():
    """Wait until market opens at 9:15 AM"""
    while True:
        current_time = get_current_time()
        current_date = current_time.date()
        
        # Check if it's a trading day
        if not is_trading_day(current_date):
            # If it's weekend, wait until Monday
            days_until_monday = (7 - current_date.weekday()) % 7
            if days_until_monday == 0:  # It's Sunday
                days_until_monday = 1
            
            next_trading_day = current_date + datetime.timedelta(days=days_until_monday)
            wait_until = datetime.datetime.combine(next_trading_day, MARKET_OPEN)
            
            print(f"📅 Non-trading day. Waiting until {wait_until.strftime('%Y-%m-%d %H:%M:%S')}")
            log_event('SYSTEM', 'wait', 'NON_TRADING_DAY', f'Waiting until {wait_until}')
            
        else:
            # It's a trading day, check if market is open
            if is_market_open():
                print("🟢 Market is OPEN!")
                return True
            
            # Market is closed, wait until it opens
            market_open_today = datetime.datetime.combine(current_date, MARKET_OPEN)
            
            if current_time < market_open_today:
                # Market hasn't opened yet today
                wait_until = market_open_today
                print(f"⏰ Waiting for market to open at {wait_until.strftime('%H:%M:%S')}")
                log_event('SYSTEM', 'wait', 'WAITING_FOR_OPEN', f'Market opens at {MARKET_OPEN}')
            else:
                # Market has closed for today, wait until tomorrow
                next_trading_day = current_date + datetime.timedelta(days=1)
                while not is_trading_day(next_trading_day):
                    next_trading_day += datetime.timedelta(days=1)
                
                wait_until = datetime.datetime.combine(next_trading_day, MARKET_OPEN)
                print(f"🔴 Market closed. Waiting until {wait_until.strftime('%Y-%m-%d %H:%M:%S')}")
                log_event('SYSTEM', 'wait', 'MARKET_CLOSED', f'Waiting until {wait_until}')
        
        # Sleep for 60 seconds before checking again
        time.sleep(60)

# ============================================================================
# API FUNCTIONS
# ============================================================================

def api_call_with_retry(api_func, *args, **kwargs):
    """Generic function to handle API calls with retry logic"""
    for attempt in range(MAX_RETRIES):
        try:
            time.sleep(API_RATE_LIMIT_DELAY)
            result = api_func(*args, **kwargs)
            
            if result.get('status') == 'success':
                return result
            else:
                error_msg = result.get('message', 'No message')
                if attempt < MAX_RETRIES - 1:
                    print(f"API call failed (attempt {attempt + 1}): {error_msg}. Retrying...")
                    time.sleep(RETRY_DELAY)
                else:
                    return result
                    
        except Exception as e:
            if attempt < MAX_RETRIES - 1:
                print(f"Exception in API call (attempt {attempt + 1}): {str(e)}. Retrying...")
                time.sleep(RETRY_DELAY)
            else:
                return {'status': 'failure', 'message': str(e), 'data': {}}
    
    return {'status': 'failure', 'message': 'Max retries exceeded', 'data': {}}

def fetch_live_market_quotes():
    """Fetch live market quotes for all stocks"""
    try:
        # Prepare securities dictionary for Market Quote API
        securities = {}
        for stock in STOCKS:
            exchange = stock['exchange_segment']
            if exchange not in securities:
                securities[exchange] = []
            securities[exchange].append(stock['security_id'])
        
        # Fetch OHLC data (includes LTP)
        data = api_call_with_retry(dhan.ohlc_data, securities)
        
        if data['status'] == 'success':
            return data['data']
        else:
            log_event('MARKET_QUOTE', 'live', 'API_ERROR', data.get('message', 'No message'))
            return {}
            
    except Exception as e:
        log_event('MARKET_QUOTE', 'live', 'EXCEPTION', str(e))
        return {}

def fetch_intraday_data(stock, timeframe, from_dt, to_dt):
    """Fetch intraday data for a specific timeframe"""
    try:
        interval = TIMEFRAMES[timeframe]
        
        data = api_call_with_retry(
            dhan.intraday_minute_data,
            security_id=stock['security_id'],
            exchange_segment=stock['exchange_segment'],
            instrument_type=stock['instrument_type'],
            interval=interval,
            from_date=from_dt.strftime('%Y-%m-%d %H:%M:%S'),
            to_date=to_dt.strftime('%Y-%m-%d %H:%M:%S')
        )
        
        if data['status'] == 'success':
            df = pd.DataFrame(data['data'])
            if not df.empty:
                # Convert UTC timestamps to IST (Indian Standard Time)
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
                df['timestamp'] = df['timestamp'].dt.tz_convert('Asia/Kolkata')
                # Remove timezone info for consistency with existing code
                df['timestamp'] = df['timestamp'].dt.tz_localize(None)
                return df
        else:
            log_event(stock['name'], timeframe, 'API_ERROR', data.get('message', 'No message'))
            
    except Exception as e:
        log_event(stock['name'], timeframe, 'EXCEPTION', str(e))
    
    return pd.DataFrame()

def fetch_daily_data(stock, from_date, to_date):
    """Fetch daily historical data"""
    try:
        data = api_call_with_retry(
            dhan.historical_daily_data,
            security_id=stock['security_id'],
            exchange_segment=stock['exchange_segment'],
            instrument_type=stock['instrument_type'],
            expiry_code=0,
            from_date=from_date.strftime('%Y-%m-%d'),
            to_date=to_date.strftime('%Y-%m-%d')
        )
        
        if data['status'] == 'success':
            df = pd.DataFrame(data['data'])
            if not df.empty:
                # Convert UTC timestamps to IST (Indian Standard Time)
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
                df['timestamp'] = df['timestamp'].dt.tz_convert('Asia/Kolkata')
                # Remove timezone info for consistency with existing code
                df['timestamp'] = df['timestamp'].dt.tz_localize(None)
                return df
        else:
            log_event(stock['name'], 'daily', 'API_ERROR', data.get('message', 'No message'))
            
    except Exception as e:
        log_event(stock['name'], 'daily', 'EXCEPTION', str(e))
    
    return pd.DataFrame()

# ============================================================================
# DATA MANAGEMENT FUNCTIONS
# ============================================================================

def save_live_quotes():
    """Save live market quotes to CSV"""
    live_quotes = fetch_live_market_quotes()

    if live_quotes:
        quote_data = []
        timestamp = get_current_time()

        for exchange, instruments in live_quotes.items():
            for security_id, quote in instruments.items():
                # Find stock name from our stocks list
                stock_name = next((s['name'] for s in STOCKS if s['security_id'] == security_id), security_id)

                quote_data.append({
                    'timestamp': timestamp,
                    'stock': stock_name,
                    'security_id': security_id,
                    'exchange': exchange,
                    'ltp': quote.get('LTP', 0),
                    'open': quote.get('open', 0),
                    'high': quote.get('high', 0),
                    'low': quote.get('low', 0),
                    'close': quote.get('close', 0),
                    'volume': quote.get('volume', 0)
                })

        if quote_data:
            df = pd.DataFrame(quote_data)
            live_file = 'stock_data/live_quotes.csv'

            # Append to existing file or create new one
            if os.path.exists(live_file):
                df.to_csv(live_file, mode='a', header=False, index=False)
            else:
                df.to_csv(live_file, mode='w', header=True, index=False)

            log_event('LIVE_QUOTES', 'all', 'UPDATE', f"Live quotes saved for {len(quote_data)} instruments")
            return True

    return False

def update_historical_data_for_minute(stock, timeframe, target_minute):
    """
    Update historical data for a specific completed minute.
    This ensures accurate OHLC data collection for the exact minute boundary.
    """
    filename = get_filename(stock, timeframe)

    # Calculate the time range for the specific minute
    from_dt = target_minute
    to_dt = target_minute + datetime.timedelta(minutes=TIMEFRAMES[timeframe])

    # Fetch data for the specific minute
    new_df = fetch_intraday_data(stock, timeframe, from_dt, to_dt)

    if not new_df.empty:
        # Filter to ensure we only get data for the target minute
        new_df = new_df[new_df['timestamp'] == target_minute]

        if not new_df.empty:
            # Load existing data
            if os.path.exists(filename):
                existing_df = pd.read_csv(filename)
                if not existing_df.empty:
                    existing_df['timestamp'] = pd.to_datetime(existing_df['timestamp'])

                    # Remove any existing data for this timestamp (avoid duplicates)
                    existing_df = existing_df[existing_df['timestamp'] != target_minute]

                    # Combine with new data
                    combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                else:
                    combined_df = new_df
            else:
                combined_df = new_df

            # Sort by timestamp
            combined_df = combined_df.sort_values('timestamp')

            # Keep only last 5 trading days of data
            trading_days = get_trading_days(HISTORICAL_DAYS)
            cutoff_date = trading_days[0]
            cutoff_datetime = datetime.datetime.combine(cutoff_date, datetime.time.min)
            combined_df = combined_df[combined_df['timestamp'] >= cutoff_datetime]

            # Save updated data
            combined_df.to_csv(filename, index=False)
            log_event(stock['name'], timeframe, 'MINUTE_UPDATE',
                     f"Updated {timeframe} for minute {target_minute.strftime('%H:%M')}: {len(new_df)} records")
        else:
            log_event(stock['name'], timeframe, 'NO_DATA',
                     f"No data available for minute {target_minute.strftime('%H:%M')}")
    else:
        log_event(stock['name'], timeframe, 'API_NO_DATA',
                 f"API returned no data for minute {target_minute.strftime('%H:%M')}")

def fill_missed_minutes(last_processed, current_minute):
    """
    Fill any missed minutes between last processed and current minute.
    This ensures no gaps in the minute data and handles all timeframes properly.
    """
    if not last_processed:
        return

    # Calculate missed minutes
    missed_minutes = []
    check_minute = last_processed + datetime.timedelta(minutes=1)

    while check_minute <= current_minute:
        missed_minutes.append(check_minute)
        check_minute += datetime.timedelta(minutes=1)

    if missed_minutes:
        print(f"🔍 Found {len(missed_minutes)} missed minutes. Filling gaps...")
        log_event('SYSTEM', 'gap_fill', 'START', f'Filling {len(missed_minutes)} missed minutes')

        for missed_minute in missed_minutes:
            print(f"📝 Filling missed minute: {missed_minute.strftime('%H:%M')}")

            # Process this missed minute with proper timeframe logic
            process_minute_data(missed_minute)

        log_event('SYSTEM', 'gap_fill', 'COMPLETE', f'Filled {len(missed_minutes)} missed minutes')

def fill_all_missing_data():
    """
    Comprehensive function to fill ALL missing data for ALL timeframes from market open to current time.
    This ensures complete data coverage regardless of when the user starts the system.
    """
    current_time = get_current_time()
    today = get_today()

    print("🔄 Starting comprehensive data filling for all timeframes...")

    # Start from market open today
    market_open_today = datetime.datetime.combine(today, MARKET_OPEN)

    # Only fill if market has opened
    if current_time < market_open_today:
        print("📅 Market hasn't opened yet today. No data to fill.")
        return

    # Calculate target minute (current minute minus buffer)
    target_minute = current_time.replace(second=0, microsecond=0)
    if current_time.second < MINUTE_COMPLETION_BUFFER:
        target_minute -= datetime.timedelta(minutes=1)

    # Ensure we don't go beyond market close
    market_close_today = datetime.datetime.combine(today, MARKET_CLOSE)
    if target_minute > market_close_today:
        target_minute = market_close_today

    print(f"📊 Target time range: {market_open_today.strftime('%H:%M')} to {target_minute.strftime('%H:%M')}")

    # Fill data for each timeframe separately to ensure completeness
    required_timeframes = ['1min', '5min', '60min']  # Focus on required timeframes

    for timeframe in required_timeframes:
        print(f"\n🎯 Filling {timeframe} data for all stocks...")
        fill_timeframe_data(timeframe, market_open_today, target_minute)

    # Fill daily data separately
    print(f"\n📅 Updating daily data for all stocks...")
    fill_daily_data()

    print("✅ Comprehensive data filling completed!")

def fill_timeframe_data(timeframe, start_time, end_time):
    """
    Fill missing data for a specific timeframe across all stocks.
    """
    interval_minutes = TIMEFRAMES[timeframe]

    # Generate all required timestamps for this timeframe
    required_timestamps = generate_timeframe_timestamps(start_time, end_time, interval_minutes)

    print(f"📈 {timeframe}: Need {len(required_timestamps)} candles from {start_time.strftime('%H:%M')} to {end_time.strftime('%H:%M')}")

    for stock in STOCKS:
        fill_stock_timeframe_data(stock, timeframe, required_timestamps)

def generate_timeframe_timestamps(start_time, end_time, interval_minutes):
    """
    Generate all required timestamps for a timeframe between start and end time.
    """
    timestamps = []
    current = start_time

    # Align to the first interval boundary
    minutes_since_market_open = (current - datetime.datetime.combine(current.date(), MARKET_OPEN)).total_seconds() / 60
    aligned_minutes = ((int(minutes_since_market_open) // interval_minutes) + 1) * interval_minutes
    current = datetime.datetime.combine(current.date(), MARKET_OPEN) + datetime.timedelta(minutes=aligned_minutes)

    while current <= end_time:
        timestamps.append(current)
        current += datetime.timedelta(minutes=interval_minutes)

    return timestamps

def fill_stock_timeframe_data(stock, timeframe, required_timestamps):
    """
    Fill missing data for a specific stock and timeframe.
    """
    filename = get_filename(stock, timeframe)

    # Get existing timestamps
    existing_timestamps = set()
    if os.path.exists(filename):
        try:
            df = pd.read_csv(filename)
            if not df.empty:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                existing_timestamps = set(df['timestamp'])
        except Exception as e:
            log_event(stock['name'], timeframe, 'ERROR', f'Error reading existing data: {str(e)}')

    # Find missing timestamps
    missing_timestamps = [ts for ts in required_timestamps if ts not in existing_timestamps]

    if missing_timestamps:
        print(f"  📝 {stock['name']} {timeframe}: Filling {len(missing_timestamps)} missing candles")

        for timestamp in missing_timestamps:
            # For 1min data, fetch the exact minute
            if timeframe == '1min':
                update_historical_data_for_minute(stock, timeframe, timestamp)
            else:
                # For other timeframes, fetch the interval data
                update_timeframe_data_for_timestamp(stock, timeframe, timestamp)
    else:
        print(f"  ✅ {stock['name']} {timeframe}: Already up-to-date")

def update_timeframe_data_for_timestamp(stock, timeframe, timestamp):
    """
    Update data for a specific timeframe timestamp (5min, 60min, etc.)
    """
    interval_minutes = TIMEFRAMES[timeframe]

    # Calculate the start time for this interval
    start_time = timestamp - datetime.timedelta(minutes=interval_minutes)
    end_time = timestamp

    # Fetch data for this interval
    new_df = fetch_intraday_data(stock, timeframe, start_time, end_time)

    if not new_df.empty:
        # Filter to get only the target timestamp
        new_df = new_df[new_df['timestamp'] == timestamp]

        if not new_df.empty:
            filename = get_filename(stock, timeframe)

            # Load existing data
            if os.path.exists(filename):
                existing_df = pd.read_csv(filename)
                if not existing_df.empty:
                    existing_df['timestamp'] = pd.to_datetime(existing_df['timestamp'])

                    # Remove any existing data for this timestamp
                    existing_df = existing_df[existing_df['timestamp'] != timestamp]

                    # Combine with new data
                    combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                else:
                    combined_df = new_df
            else:
                combined_df = new_df

            # Sort by timestamp
            combined_df = combined_df.sort_values('timestamp')

            # Save updated data
            combined_df.to_csv(filename, index=False)
            log_event(stock['name'], timeframe, 'TIMEFRAME_UPDATE',
                     f"Updated {timeframe} for timestamp {timestamp.strftime('%H:%M')}: {len(new_df)} records")
        else:
            log_event(stock['name'], timeframe, 'NO_DATA',
                     f"No data available for {timeframe} timestamp {timestamp.strftime('%H:%M')}")
    else:
        log_event(stock['name'], timeframe, 'API_NO_DATA',
                 f"API returned no data for {timeframe} timestamp {timestamp.strftime('%H:%M')}")

def fill_daily_data():
    """
    Ensure daily data is up-to-date for all stocks.
    """
    for stock in STOCKS:
        update_historical_data(stock, 'daily')

def show_data_status():
    """
    Show the current status of data files for all stocks and timeframes.
    """
    print("\n📊 Current Data Status:")
    print("=" * 80)

    required_timeframes = ['1min', '5min', '60min', 'daily']

    for stock in STOCKS:
        print(f"\n📈 {stock['name']}:")
        for timeframe in required_timeframes:
            filename = get_filename(stock, timeframe)
            if os.path.exists(filename):
                try:
                    df = pd.read_csv(filename)
                    if not df.empty:
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                        first_time = df['timestamp'].min().strftime('%H:%M')
                        last_time = df['timestamp'].max().strftime('%H:%M')
                        count = len(df)
                        print(f"  ✅ {timeframe:6}: {count:3} records ({first_time} to {last_time})")
                    else:
                        print(f"  ❌ {timeframe:6}: Empty file")
                except Exception as e:
                    print(f"  ❌ {timeframe:6}: Error reading file - {str(e)}")
            else:
                print(f"  ❌ {timeframe:6}: File not found")

    print("=" * 80)

def update_historical_data(stock, timeframe):
    """Update historical data for a stock and timeframe (legacy function for compatibility)"""
    if timeframe == 'daily':
        # For daily data, keep last 3 days (rolling window)
        filename = get_filename(stock, timeframe)
        trading_days = get_trading_days(DAILY_DATA_DAYS)
        from_date = trading_days[0]
        to_date = trading_days[-1]

        # Fetch daily data
        df = fetch_daily_data(stock, from_date, to_date)

        if not df.empty:
            # Save daily data (replace entire file to maintain rolling window)
            df.to_csv(filename, index=False)
            log_event(stock['name'], 'daily', 'UPDATE', f"Updated daily data: {len(df)} records")

    else:
        # For intraday data, use minute-accurate method
        current_minute = get_last_completed_minute()
        update_historical_data_for_minute(stock, timeframe, current_minute)

def cleanup_old_data():
    """Clean up data older than retention period"""
    trading_days = get_trading_days(HISTORICAL_DAYS)
    cutoff_date = trading_days[0]

    for stock in STOCKS:
        for timeframe in TIMEFRAMES:
            if timeframe == 'daily':
                continue  # Daily data is managed separately

            filename = get_filename(stock, timeframe)
            if os.path.exists(filename):
                df = pd.read_csv(filename)
                if not df.empty:
                    # Timestamps in CSV are already in IST format
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    cutoff_datetime = datetime.datetime.combine(cutoff_date, datetime.time.min)

                    # Keep only data from last 5 trading days
                    filtered_df = df[df['timestamp'] >= cutoff_datetime]

                    if len(filtered_df) < len(df):
                        filtered_df.to_csv(filename, index=False)
                        removed_count = len(df) - len(filtered_df)
                        log_event(stock['name'], timeframe, 'CLEANUP', f"Removed {removed_count} old records")

# ============================================================================
# MAIN EXECUTION FUNCTIONS
# ============================================================================

def run_live_data_collection():
    """Run live data collection during market hours with proper minute-boundary synchronization"""
    print("🚀 Starting live data collection with accurate minute-boundary timing...")
    log_event('SYSTEM', 'live', 'START', 'Live data collection started with minute accuracy')

    last_live_fetch = get_current_time() - datetime.timedelta(seconds=LIVE_DATA_INTERVAL)

    # Get the last processed minute from existing data to avoid gaps
    last_minute_processed = get_last_processed_minute_from_data()

    current_time = get_current_time()
    print(f"⏰ Current time: {current_time.strftime('%H:%M:%S')}")
    if last_minute_processed:
        print(f"📊 Last processed minute: {last_minute_processed.strftime('%H:%M:%S')}")

    # Fill any gaps from last processed minute to current time
    if last_minute_processed:
        current_minute_boundary = current_time.replace(second=0, microsecond=0)
        if current_time.second < MINUTE_COMPLETION_BUFFER:
            current_minute_boundary -= datetime.timedelta(minutes=1)
        fill_missed_minutes(last_minute_processed, current_minute_boundary)

    while is_market_open() and is_trading_day(get_today()):
        current_time = get_current_time()

        # Fetch live quotes at regular intervals (for real-time monitoring)
        if (current_time - last_live_fetch).total_seconds() >= LIVE_DATA_INTERVAL:
            print(f"📊 Fetching live quotes at {current_time.strftime('%H:%M:%S')}")
            save_live_quotes()
            last_live_fetch = current_time

        # Check if we need to process a completed minute
        completed_minute = get_next_minute_to_process(last_minute_processed, current_time)

        if completed_minute:
            print(f"🎯 Processing completed minute: {completed_minute.strftime('%H:%M:%S')}")
            log_event('SYSTEM', 'minute_sync', 'PROCESS', f'Processing minute: {completed_minute.strftime("%H:%M")}')

            # Process all timeframes for this completed minute
            process_minute_data(completed_minute)

            last_minute_processed = completed_minute

        # Sleep for 5 seconds before next check
        time.sleep(5)

    log_event('SYSTEM', 'live', 'END', 'Live data collection ended')
    print("🔴 Live data collection ended (market closed)")

def run_historical_data_update():
    """Update historical data when market is closed"""
    print("📚 Updating historical data...")
    log_event('SYSTEM', 'historical', 'START', 'Historical data update started')

    for stock in STOCKS:
        print(f"Updating data for {stock['name']}...")

        # Update all timeframes
        for timeframe in TIMEFRAMES:
            update_historical_data(stock, timeframe)
            time.sleep(0.1)  # Small delay between requests

    # Cleanup old data
    cleanup_old_data()

    log_event('SYSTEM', 'historical', 'END', 'Historical data update completed')
    print("✅ Historical data update completed")

def main():
    """Main execution function"""
    print("=" * 60)
    print("🚀 DhanHQ Enhanced Live Data Fetcher - Minute Accurate Version")
    print("=" * 60)
    print(f"📅 Current Time: {get_current_time().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🕘 Market Hours: {MARKET_OPEN} - {MARKET_CLOSE}")
    print(f"📊 Stocks: {', '.join([s['name'] for s in STOCKS])}")
    print(f"⏱️  Timeframes: {', '.join(TIMEFRAMES.keys())}")
    print(f"📈 Historical Days: {HISTORICAL_DAYS} trading days")
    print(f"📊 Daily Data Days: {DAILY_DATA_DAYS} days")
    print(f"⏰ Minute Completion Buffer: {MINUTE_COMPLETION_BUFFER} seconds")
    print(f"🎯 Minute Sync Tolerance: {MINUTE_SYNC_TOLERANCE} seconds")
    print("=" * 60)

    # Show current data status
    show_data_status()

    # Comprehensive data filling for all timeframes
    print("\n🔄 Performing comprehensive data filling for all timeframes...")
    print("📊 Ensuring all CSV files are up-to-date from market open to current time...")
    fill_all_missing_data()

    # Show updated data status
    show_data_status()

    # Main loop - wait for market and collect live data
    while True:
        try:
            # Wait for market to open
            wait_for_market_open()

            # Market is open, start live data collection
            run_live_data_collection()

            # Market closed, update historical data
            print("\n🔄 Market closed. Updating historical data...")
            run_historical_data_update()

            # Wait a bit before checking market status again
            print("\n⏰ Waiting for next market session...")
            time.sleep(300)  # Wait 5 minutes before checking again

        except KeyboardInterrupt:
            print("\n\n🛑 Stopping data collection...")
            log_event('SYSTEM', 'main', 'STOP', 'Manual stop requested')
            break
        except Exception as e:
            print(f"\n❌ Error in main loop: {str(e)}")
            log_event('SYSTEM', 'main', 'ERROR', str(e))
            print("⏰ Waiting 60 seconds before retry...")
            time.sleep(60)

    print("👋 Data collection stopped.")

if __name__ == "__main__":
    main()
